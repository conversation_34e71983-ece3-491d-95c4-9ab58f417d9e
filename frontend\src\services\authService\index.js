import axios from "axios";
import config from "../../config";

export const authService = {
  // API Service Methods
  userLoginService: (data) => {
    return axios.post(`${config.LOGIN_URL}`, data);
  },
  sessionValidationService: (data) => {
    return axios.post(`${config.FORGET_URL}/validate_session`, data);
  },
  userRegisterService: (data) => {
    return axios.post(`${config.API_URL}/api/auth/register`, data);
  },
  userForgetService: (data) => {
    return axios.post(`${config.FORGET_URL}/forgot_password`, data);
  },
  userResetPasswordService: (data) => {
    return axios.post(`${config.FORGET_URL}/reset_password`, data);
  },
  userLogoutService: (data) => {
    return axios.post(`${config.API_URL}/api/auth/logout`, data);
  },
  userVerifyEmailService: (data) => {
    return axios.get(`${config.API_URL}/api/auth/verify_email`, data);
  },
  userVerifyOTPService: (data) => {
    return axios.post(`${config.API_URL}/api/auth/verify-otp`, data);
  },
};
